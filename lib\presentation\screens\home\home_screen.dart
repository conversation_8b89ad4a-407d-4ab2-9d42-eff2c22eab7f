import 'package:quranic_insights/presentation/screens/home/<USER>/hijri_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import '/core/utils/constants/extensions/extensions.dart';
import '/core/utils/responsive_helper.dart';
import '../../../core/widgets/tab_bar_widget.dart';
import '../../controllers/theme_controller.dart';
import 'widgets/ayah_tafsir_widget.dart';
import 'widgets/daily_zeker.dart';
import 'widgets/last_read.dart';
import 'widgets/screens_list.dart';
import '/features/settings/screens/merged_settings_screen.dart';
import '/features/admin/screens/admin_dashboard_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(builder: (_) {
      return ScreenUtilInit(
          child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.primary,
        body: SafeArea(
          child: Container(
            color: Theme.of(context).colorScheme.primaryContainer,
            child: ResponsiveBuilder(
              mobile: (context, constraints) => _buildMobileLayout(),
              tablet: (context, constraints) => _buildTabletLayout(),
              desktop: (context, constraints) => _buildDesktopLayout(),
            ),
          ),
        ),
      ));
    });
  }

  Widget _buildMobileLayout() {
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        const Gap(8),
        Center(child: HijriWidget()),
        const Gap(16),
        const ScreensList(),
        const Gap(8),
        LastRead(),
        AyahTafsirWidget(),
        const Gap(16),
        DailyZeker(),
        const Gap(16),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return ResponsiveContainer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const Gap(8),
          Row(
            children: [
              Expanded(
                flex: 5,
                child: Column(
                  children: [
                    HijriWidget(),
                    const Gap(16),
                    LastRead(),
                  ],
                ),
              ),
              const Gap(16),
              const Expanded(flex: 5, child: ScreensList()),
            ],
          ),
          const Gap(24),
          AyahTafsirWidget(),
          const Gap(24),
          DailyZeker(),
          const Gap(24),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return ResponsiveContainer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const Gap(16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    HijriWidget(),
                    const Gap(24),
                    const ScreensList(),
                  ],
                ),
              ),
              const Gap(32),
              Expanded(
                flex: 4,
                child: Column(
                  children: [
                    LastRead(),
                    const Gap(24),
                    AyahTafsirWidget(),
                  ],
                ),
              ),
              const Gap(32),
              Expanded(
                flex: 3,
                child: DailyZeker(),
              ),
            ],
          ),
          const Gap(32),
        ],
      ),
    );
  }
}
