import 'package:flutter/foundation.dart' show kIsWeb;
import 'core/utils/platform_check.dart';

import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_storage/get_storage.dart';
import 'package:timezone/data/latest.dart' as tz;

import '/core/services/languages/dependency_inj.dart' as dep;
import 'core/config/environment.dart';
import 'core/services/background_services.dart';
import 'core/services/notifications_helper.dart';
import 'core/services/services_locator.dart';
import 'core/services/supabase/supabase_client.dart';
import 'core/utils/constants/shared_preferences_constants.dart';
import 'myApp.dart';

// Conditional import for platform configuration
import 'platform_config/platform_config_stub.dart'
    if (dart.library.io) 'platform_config/platform_config_native.dart'
    if (dart.library.html) 'platform_config/platform_config_web.dart';

Future<void> main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  widgetsBinding;
  
  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    print('[FLUTTER ERROR] ${details.exception}');
    print('[FLUTTER ERROR] Stack trace:\n${details.stack}');
  };
  
  if (kIsWeb) {
    print('Running on Web platform');
  }
  
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  
  try {
    print('Initializing languages...');
    Map<String, Map<String, String>> languages = await dep.init();
    print('Languages initialized');
    
    print('Starting app initialization...');
    await initializeApp();
    print('App initialization complete');
    
    configurePlatform(); // Platform-specific configuration
    
    print('Starting MyApp...');
    runApp(MyApp(
      languages: languages,
    ));
  } catch (e, stackTrace) {
    print('[MAIN ERROR] Failed to initialize app: $e');
    print('[MAIN ERROR] Stack trace:\n$stackTrace');
    
    // Show a simple error app
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('Failed to initialize app: $e'),
            ],
          ),
        ),
      ),
    ));
  }
}

Future<void> initializeApp() async {
  Future.delayed(const Duration(seconds: 0));
  
  print('Initializing GetStorage...');
  await GetStorage.init();
  print('GetStorage initialized');
  
  // Load environment variables
  print('Loading environment...');
  await Environment.load();
  print('Environment loaded');
  
  // Validate environment
  print('Validating environment...');
  Environment.validate();
  print('Environment validated');
  
  // Initialize Supabase
  print('Initializing Supabase...');
  await SupabaseService.initialize();
  print('Supabase initialized');
  
  if (!kIsWeb) {
    NotifyHelper.initAwesomeNotifications();
  }
  
  print('Initializing ServicesLocator...');
  await ServicesLocator().init();
  print('ServicesLocator initialized');
  
  tz.initializeTimeZones();
  
  if (PlatformCheck.isMobile) {
    await BGServices().registerTask();
  }
  
  print('Removing native splash...');
  FlutterNativeSplash.remove();
  
  GetStorage().write(AUDIO_SERVICE_INITIALIZED, false);
  // try {
  //   await WakelockPlus.enable();
  // } catch (e) {
  //   print('Failed to enable wakelock: $e');
  // }
}
