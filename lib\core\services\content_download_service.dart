import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quranic_insights/core/providers/supabase_provider.dart';
import 'package:quranic_insights/core/utils/platform_utils.dart';
import 'package:quranic_insights/core/utils/io_utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

// Provider for content download service
final contentDownloadServiceProvider = Provider<ContentDownloadService>((ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return ContentDownloadService(supabase.storage);
});

class ContentDownloadService {
  final SupabaseStorageClient _storage;
  final _dio = Dio();
  
  ContentDownloadService(this._storage);
  
  // Base URL for Supabase storage
  static const String _baseUrl = 'https://akhtiyivutfqwsjiltno.supabase.co/storage/v1/object/public';
  
  // Available tafsir databases
  static const Map<String, TafsirInfo> availableTafsirs = {
    'saadi': TafsirInfo(
      name: 'Tafsir As-Sa\'di',
      fileName: 'saadiV4.db',
      size: 7598080, // 7.5MB
      isDefault: true,
    ),
    'baghawi': TafsirInfo(
      name: 'Tafsir Al-Baghawi',
      fileName: 'baghawyV3.db',
      size: 9093120, // 9MB
      isDefault: false,
    ),
    'ibnkatheer': TafsirInfo(
      name: 'Tafsir Ibn Kathir',
      fileName: 'ibnkatheerV3.sqlite',
      size: 17477632, // 17MB
      isDefault: false,
    ),
    'qurtubi': TafsirInfo(
      name: 'Tafsir Al-Qurtubi',
      fileName: 'qurtubiV3.db',
      size: 22548480, // 22MB
      isDefault: false,
    ),
    'tabari': TafsirInfo(
      name: 'Tafsir At-Tabari',
      fileName: 'tabariV3.db',
      size: 38240256, // 38MB
      isDefault: false,
    ),
  };
  
  /// Get the local directory for storing downloaded content
  Future<String> _getContentDirectory() async {
    final appDirPath = await PlatformUtils.getApplicationDocumentsDirectory();
    final contentDirPath = '$appDirPath/tafsir_databases';
    
    // Create directory if it doesn't exist (only on non-web platforms)
    if (!kIsWeb) {
      await IOUtils.instance.createDirectory(contentDirPath, recursive: true);
    }
    
    return contentDirPath;
  }
  
  /// Check if a tafsir is already downloaded
  Future<bool> isTafsirDownloaded(String tafsirKey) async {
    final tafsirInfo = availableTafsirs[tafsirKey];
    if (tafsirInfo == null) return false;
    
    if (PlatformUtils.isWeb) {
      // On web, check if file exists in GetStorage
      return await PlatformUtils.fileExists('tafsir_databases/${tafsirInfo.fileName}');
    } else {
      if (!kIsWeb) {
        final contentDirPath = await _getContentDirectory();
        return await IOUtils.instance.fileExists('$contentDirPath/${tafsirInfo.fileName}');
      }
      return false;
    }
  }
  
  /// Download a tafsir database with progress tracking
  Future<void> downloadTafsir(
    String tafsirKey, {
    void Function(double progress)? onProgress,
    void Function(String error)? onError,
  }) async {
    try {
      final tafsirInfo = availableTafsirs[tafsirKey];
      if (tafsirInfo == null) {
        onError?.call('Tafsir not found: $tafsirKey');
        return;
      }
      
      final contentDirPath = await _getContentDirectory();
      final filePath = '$contentDirPath/${tafsirInfo.fileName}';
      final tempFilePath = '$filePath.tmp';
      
      // Download URL
      final url = '$_baseUrl/tafsir-databases/${tafsirInfo.fileName}';
      
      if (PlatformUtils.isWeb) {
        // On web, download to memory and save using platform utils
        final response = await _dio.get<List<int>>(
          url,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              final progress = received / total;
              onProgress?.call(progress);
            }
          },
          options: Options(
            responseType: ResponseType.bytes,
            headers: {
              'Accept': '*/*',
            },
          ),
        );
        
        if (response.data != null) {
          await PlatformUtils.saveFile(
            'tafsir_databases/${tafsirInfo.fileName}',
            response.data!,
          );
        }
      } else {
        // On native platforms, download directly to file
        await _dio.download(
          url,
          tempFilePath,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              final progress = received / total;
              onProgress?.call(progress);
            }
          },
          options: Options(
            headers: {
              'Accept': '*/*',
            },
          ),
        );
        
        // Rename temp file to final file
        if (!kIsWeb) {
          await IOUtils.instance.renameFile(tempFilePath, filePath);
        }
      }
      
    } catch (e) {
      onError?.call('Download failed: $e');
    }
  }
  
  /// Get list of downloaded tafsirs
  Future<List<String>> getDownloadedTafsirs() async {
    final downloaded = <String>[];
    for (final key in availableTafsirs.keys) {
      if (await isTafsirDownloaded(key)) {
        downloaded.add(key);
      }
    }
    return downloaded;
  }
  
  /// Delete a downloaded tafsir
  Future<void> deleteTafsir(String tafsirKey) async {
    final tafsirInfo = availableTafsirs[tafsirKey];
    if (tafsirInfo == null) return;
    
    if (PlatformUtils.isWeb) {
      await PlatformUtils.deleteFile('tafsir_databases/${tafsirInfo.fileName}');
    } else {
      if (!kIsWeb) {
        final contentDirPath = await _getContentDirectory();
        await IOUtils.instance.deleteFile('$contentDirPath/${tafsirInfo.fileName}');
      }
    }
  }
  
  /// Get total size of downloaded content
  Future<int> getDownloadedContentSize() async {
    int totalSize = 0;
    
    if (PlatformUtils.isWeb) {
      // On web, estimate size based on downloaded tafsirs
      for (final key in availableTafsirs.keys) {
        if (await isTafsirDownloaded(key)) {
          totalSize += availableTafsirs[key]!.size;
        }
      }
    } else {
      if (!kIsWeb) {
        final contentDirPath = await _getContentDirectory();
        
        if (await IOUtils.instance.directoryExists(contentDirPath)) {
          await for (final path in IOUtils.instance.listDirectory(contentDirPath)) {
            if (path.endsWith('.db')) {
              totalSize += await IOUtils.instance.getFileLength(path);
            }
          }
        }
      }
    }
    
    return totalSize;
  }
  
  /// Download essential content on first run
  Future<void> downloadEssentialContent({
    void Function(double progress)? onProgress,
  }) async {
    // Download default tafsir if not already downloaded
    if (!await isTafsirDownloaded('saadi')) {
      await downloadTafsir('saadi', onProgress: onProgress);
    }
  }
}

class TafsirInfo {
  final String name;
  final String fileName;
  final int size;
  final bool isDefault;
  
  const TafsirInfo({
    required this.name,
    required this.fileName,
    required this.size,
    required this.isDefault,
  });
}