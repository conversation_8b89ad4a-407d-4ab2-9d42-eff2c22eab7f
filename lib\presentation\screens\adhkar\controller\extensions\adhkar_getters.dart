import 'dart:developer';
import 'dart:math' as math;

import 'package:get/get.dart';

import '../../../../../core/utils/constants/shared_preferences_constants.dart';
import '../../../../../database/bookmark_db/adhkar_models.dart';
import '../../../calendar/events.dart';
import '../adhkar_controller.dart';

extension AdhkarGetters on AzkarController {
  /// -------- [Getters] ----------
  bool get hasZekerSettedForThisDay {
    final settedDate = state.box.read(SETTED_DATE_FOR_ZEKER);
    return (settedDate != null &&
        settedDate == EventController.instance.hijriNow.fullDate());
  }

  Future<AdhkarData> getDailyDhekr() async {
    try {
      if (state.dhekrOfTheDay != null) return state.dhekrOfTheDay!;
      
      // Ensure adhkar data is loaded
      if (state.allAdhkar.isEmpty) {
        print('Waiting for Adhkar data to load...');
        await fetchDhekr();
        // Wait a bit more if still empty
        if (state.allAdhkar.isEmpty) {
          await Future.delayed(const Duration(seconds: 1));
          await fetchDhekr();
        }
      }
      
      if (state.allAdhkar.isEmpty) {
        print('ERROR: No Adhkar loaded - returning default');
        // Return a default dhekr if data still not loaded
        return AdhkarData(
          id: 1,
          category: 'أذكار الصباح',
          count: '1',
          description: '',
          reference: '',
          zekr: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
        );
      }
      
      final String? zekerOfTheDayIdAndId =
          state.box.read(ZEKER_OF_THE_DAY_AND_ID);
      state.dhekrOfTheDay = await _getZekerForThisDay(
          hasZekerSettedForThisDay ? zekerOfTheDayIdAndId : null);

      return state.dhekrOfTheDay!;
    } catch (e) {
      print('Error getting daily Dhekr: $e');
      // Return default dhekr on error
      return AdhkarData(
        id: 1,
        category: 'أذكار الصباح',
        count: '1',
        description: '',
        reference: '',
        zekr: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
      );
    }
  }

  Future<AdhkarData> _getZekerForThisDay(
      [String? zekerOfTheDayIdAndZekerId]) async {
    log("zekerOfTheDayIdAndZekerId: ${zekerOfTheDayIdAndZekerId == null ? "null" : "NOT NULL"}");
    
    // Check if allAdhkar is empty
    if (state.allAdhkar.isEmpty) {
      log("allAdhkar is empty, returning default");
      return AdhkarData(
        id: 1,
        category: 'أذكار الصباح',
        count: '1',
        description: '',
        reference: '',
        zekr: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
      );
    }
    
    if (zekerOfTheDayIdAndZekerId != null) {
      log("before trying to get ziker", name: 'BEFORE');
      final index = int.parse(zekerOfTheDayIdAndZekerId) - 1;
      if (index >= 0 && index < state.allAdhkar.length) {
        final cachedZeker = state.allAdhkar[index];
        log("date: ${EventController.instance.hijriNow.fullDate()}",
            name: 'CAHECH HADITH');
        return cachedZeker;
      }
    }
    
    final random = math.Random().nextInt(state.allAdhkar.length);
    log('allAzkar length: ${state.allAdhkar.length}');
    AdhkarData? zeker = state.allAdhkar.firstWhereOrNull((z) => z.id == random);
    
    // If not found by id, just get a random one by index
    if (zeker == null && state.allAdhkar.isNotEmpty) {
      zeker = state.allAdhkar[random % state.allAdhkar.length];
    }
    
    log('before listing');
    // Defer the write operation to avoid setState during build
    if (zeker != null) {
      Future.microtask(() {
        state.box
          ..write(ZEKER_OF_THE_DAY_AND_ID, '${zeker!.id}')
          ..write(
              SETTED_DATE_FOR_ZEKER, EventController.instance.hijriNow.fullDate());
      });
    }
    
    return zeker ?? AdhkarData(
      id: 1,
      category: 'أذكار الصباح',
      count: '1',
      description: '',
      reference: '',
      zekr: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
    );
  }
}
