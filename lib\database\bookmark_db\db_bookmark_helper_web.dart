// Web implementation of DbBookmarkHelper
// This is a placeholder that provides the same API but doesn't use drift

import 'dart:convert';
import 'package:get_storage/get_storage.dart';

// Import model types
import '../../presentation/screens/quran_page/data/model/bookmark_model.dart';
import '../../presentation/screens/quran_page/data/model/bookmark_ayah_model.dart';
import 'adhkar_models.dart';
// Also export the model types
export '../../presentation/screens/quran_page/data/model/bookmark_model.dart' show Bookmark, BookmarksCompanion;
export '../../presentation/screens/quran_page/data/model/bookmark_ayah_model.dart' show BookmarkAyah, BookmarksAyahsCompanion;
export 'adhkar_models.dart' show AdhkarData, AdhkarCompanion;

class DbBookmarkHelper {
  static final _storage = GetStorage();
  static const _bookmarksKey = 'web_bookmarks';
  static const _bookmarkTextsKey = 'web_bookmark_texts';
  static const _adhkarKey = 'web_adhkar';
  static Future<int?> addAdhkar(dynamic adhkar) async {
    print('Web: addAdhkar not implemented');
    return null;
  }

  static Future<int?> addBookmark(dynamic bookmark) async {
    try {
      final List<Bookmark> bookmarks = await queryB();
      
      // Generate new ID
      final newId = bookmarks.isEmpty ? 1 : bookmarks.map((b) => b.id!).reduce((a, b) => a > b ? a : b) + 1;
      
      // Create new bookmark
      final newBookmark = Bookmark(
        id: newId,
        sorahName: bookmark.sorahName.value,
        pageNum: bookmark.pageNum.value,
        lastRead: bookmark.lastRead.value,
      );
      
      bookmarks.add(newBookmark);
      
      // Save to storage
      final jsonList = bookmarks.map((b) => {
        'id': b.id,
        'sorahName': b.sorahName,
        'pageNum': b.pageNum,
        'lastRead': b.lastRead,
      }).toList();
      
      await _storage.write(_bookmarksKey, json.encode(jsonList));
      return newId;
    } catch (e) {
      print('Error adding bookmark to web storage: $e');
      return null;
    }
  }

  static Future<int?> addBookmarkText(dynamic bookmarkText) async {
    try {
      final List<BookmarkAyah> bookmarkTexts = await queryT();
      
      // Generate new ID
      final newId = bookmarkTexts.isEmpty ? 1 : bookmarkTexts.map((b) => b.id!).reduce((a, b) => a > b ? a : b) + 1;
      
      // Create new bookmark text
      final newBookmarkText = BookmarkAyah(
        id: newId,
        surahName: bookmarkText.surahName.value,
        pageNum: bookmarkText.pageNumber.value,
        ayahNum: bookmarkText.ayahNumber.value,
        ayahUQNumber: bookmarkText.ayahUQNumber.value,
        lastRead: bookmarkText.lastRead.value,
      );
      
      bookmarkTexts.add(newBookmarkText);
      
      // Save to storage
      final jsonList = bookmarkTexts.map((b) => {
        'id': b.id,
        'surahName': b.surahName,
        'pageNum': b.pageNum,
        'ayahNum': b.ayahNum,
        'ayahUQNumber': b.ayahUQNumber,
        'lastRead': b.lastRead,
      }).toList();
      
      await _storage.write(_bookmarkTextsKey, json.encode(jsonList));
      return newId;
    } catch (e) {
      print('Error adding bookmark text to web storage: $e');
      return null;
    }
  }

  static Future<int> deleteAllAdhkar() async {
    print('Web: deleteAllAdhkar not implemented');
    return 0;
  }

  static Future<int> deleteAdhkar(String category, String zekr) async {
    print('Web: deleteAdhkar not implemented');
    return 0;
  }

  static Future<int> deleteAllBookmarks() async {
    print('Web: deleteAllBookmarks not implemented');
    return 0;
  }

  static Future<int> deleteAllBookmarksText() async {
    print('Web: deleteAllBookmarksText not implemented');
    return 0;
  }

  static Future<int> deleteBookmark(dynamic bookmark) async {
    try {
      final List<Bookmark> bookmarks = await queryB();
      final initialLength = bookmarks.length;
      
      // Remove bookmark by pageNum (which is how it's identified in the controller)
      bookmarks.removeWhere((b) => b.pageNum == bookmark.pageNum);
      
      // Save updated list
      final jsonList = bookmarks.map((b) => {
        'id': b.id,
        'sorahName': b.sorahName,
        'pageNum': b.pageNum,
        'lastRead': b.lastRead,
      }).toList();
      
      await _storage.write(_bookmarksKey, json.encode(jsonList));
      return initialLength - bookmarks.length;
    } catch (e) {
      print('Error deleting bookmark from web storage: $e');
      return 0;
    }
  }

  static Future<int> deleteBookmarkText(dynamic bookmarkText) async {
    print('Web: deleteBookmarkText not implemented');
    return 0;
  }

  static Future<List<AdhkarData>> getAllAdhkar() async {
    print('Web: getAllAdhkar not implemented');
    return <AdhkarData>[];
  }

  static Future<List<Bookmark>> queryB() async {
    try {
      final data = _storage.read(_bookmarksKey);
      if (data == null) return <Bookmark>[];
      
      final List<dynamic> jsonList = json.decode(data);
      return jsonList.map((json) => Bookmark(
        id: json['id'] ?? 0,
        sorahName: json['sorahName'] ?? '',
        pageNum: json['pageNum'] ?? 0,
        lastRead: json['lastRead'] ?? '',
      )).toList();
    } catch (e) {
      print('Error loading bookmarks from web storage: $e');
      return <Bookmark>[];
    }
  }

  static Future<List<AdhkarData>> queryC() async {
    print('Web: queryC not implemented');
    return <AdhkarData>[];
  }

  static Future<List<BookmarkAyah>> queryT() async {
    try {
      final data = _storage.read(_bookmarkTextsKey);
      if (data == null) return <BookmarkAyah>[];
      
      final List<dynamic> jsonList = json.decode(data);
      return jsonList.map((jsonItem) => BookmarkAyah(
        id: jsonItem['id'] ?? 0,
        surahName: jsonItem['surahName'] ?? '',
        pageNum: jsonItem['pageNum']?.toString() ?? '0',
        ayahNum: jsonItem['ayahNum'] ?? 0,
        ayahUQNumber: jsonItem['ayahUQNumber'] ?? 0,
        lastRead: jsonItem['lastRead'] ?? '',
      )).toList();
    } catch (e) {
      print('Error loading bookmark texts from web storage: $e');
      return <BookmarkAyah>[];
    }
  }

  static Future<int> updateAdhkar(dynamic adhkar, int id) async {
    print('Web: updateAdhkar not implemented');
    return 0;
  }

  static Future<int> updateBookmarks(dynamic bookmark) async {
    print('Web: updateBookmarks not implemented');
    return 0;
  }

  static Future<int> updateBookmarksText(dynamic bookmarkText) async {
    print('Web: updateBookmarksText not implemented');
    return 0;
  }
}