import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:get_storage/get_storage.dart';
import 'io_utils.dart';

// Conditional import for path_provider
import 'package:path_provider/path_provider.dart' as path_provider
    if (dart.library.html) 'platform_utils_web_stub.dart';

class PlatformUtils {
  static bool get isWeb => kIsWeb;
  
  static final GetStorage _webStorage = GetStorage('tafsir_content');
  
  /// Get application documents directory path
  static Future<String> getApplicationDocumentsDirectory() async {
    if (isWeb) {
      return '/web_documents';
    } else {
      final dir = await path_provider.getApplicationDocumentsDirectory();
      return dir.path;
    }
  }
  
  /// Check if a file exists
  static Future<bool> fileExists(String path) async {
    if (isWeb) {
      await _webStorage.initStorage;
      return _webStorage.hasData(path);
    } else {
      // On native platforms, use IO utils
      if (!kIsWeb) {
        return await IOUtils.instance.fileExists(path);
      }
      return false;
    }
  }
  
  /// Save file (web-compatible)
  static Future<void> saveFile(String path, List<int> bytes) async {
    if (isWeb) {
      await _webStorage.initStorage;
      await _webStorage.write(path, bytes);
    } else if (!kIsWeb) {
      await IOUtils.instance.writeAsBytes(path, bytes);
    }
  }
  
  /// Delete file (web-compatible)
  static Future<void> deleteFile(String path) async {
    if (isWeb) {
      await _webStorage.initStorage;
      await _webStorage.remove(path);
    } else if (!kIsWeb) {
      await IOUtils.instance.deleteFile(path);
    }
  }
  
  /// Read file (web-compatible)
  static Future<List<int>?> readFile(String path) async {
    if (isWeb) {
      await _webStorage.initStorage;
      final data = _webStorage.read<List<dynamic>>(path);
      return data?.cast<int>();
    } else if (!kIsWeb) {
      final bytes = await IOUtils.instance.readAsBytes(path);
      return bytes?.toList();
    }
    return null;
  }
}