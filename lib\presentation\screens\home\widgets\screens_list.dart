import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '/core/utils/constants/extensions/extensions.dart';
import '/core/widgets/container_with_lines.dart';
import '/core/utils/responsive_helper.dart';
import '/core/widgets/adaptive_navigation.dart';
import '../../../../core/utils/constants/lists.dart';
import '../../../../core/widgets/elevated_button_widget.dart';

class ScreensList extends StatelessWidget {
  const ScreensList({super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);
    final isDesktop = ResponsiveHelper.isDesktop(context);
    
    // Adaptive sizing
    final containerWidth = isDesktop ? 600.0 : (isTablet ? 500.0 : 390.0);
    final itemHeight = isDesktop ? 90.0 : (isTablet ? 80.0 : 70.0);
    final crossAxisCount = isDesktop ? 3 : (isTablet ? 2 : 2);
    
    return ContainerWithLines(
      width: containerWidth,
      linesColor: Theme.of(context).colorScheme.primary,
      containerColor:
          Theme.of(context).colorScheme.primary.withValues(alpha: .2),
      child: Padding(
        padding: EdgeInsets.all(isDesktop ? 16.0 : 8.0),
        child: isDesktop
            ? _buildGridLayout(context, crossAxisCount, itemHeight)
            : _buildWrapLayout(context, itemHeight),
      ),
    );
  }

  Widget _buildGridLayout(BuildContext context, int crossAxisCount, double itemHeight) {
    final filteredList = screensList.where((item) => screensList.indexOf(item) != 0).toList();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5,
      ),
      itemCount: filteredList.length,
      itemBuilder: (context, index) {
        final item = filteredList[index];
        return HoverableCard(
          onTap: () => Get.to(item['route'], transition: Transition.downToUp),
          child: _buildScreenItem(context, item, screensList.indexOf(item), itemHeight),
        );
      },
    );
  }

  Widget _buildWrapLayout(BuildContext context, double itemHeight) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      runSpacing: 8,
      children: List.generate(
        screensList.length,
        (index) => index == 0
            ? const SizedBox.shrink()
            : GestureDetector(
                onTap: () {
                  Get.to(screensList[index]['route'],
                      transition: Transition.downToUp);
                },
                child: ElevatedButtonWidget(
                  index: index,
                  onClick: () => Get.to(screensList[index]['route'],
                      transition: Transition.downToUp),
                  height: itemHeight,
                  width: index == 5
                      ? screensList[index]['width'] - 5
                      : screensList[index]['width'] + 5,
                  child: _buildScreenItem(context, screensList[index], index, itemHeight),
                ),
              ),
      ),
    );
  }

  Widget _buildScreenItem(BuildContext context, Map<String, dynamic> item, int index, double itemHeight) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    // Special height for tafsir library (index 5) and settings (index 7)
    final iconHeight = isDesktop 
        ? ((index == 5 || index == 7) ? 45 : 75) 
        : ((index == 5 || index == 7) ? 35 : 65);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SvgPicture.asset(
          item['svgUrl'],
          height: iconHeight.toDouble(),
        ),
        if (index != 2 && index != 3)
          Container(
            width: index == 5 ? (isDesktop ? 200 : 190) : (isDesktop ? 160 : 150),
            margin: const EdgeInsets.symmetric(horizontal: 8.0),
            padding: const EdgeInsets.symmetric(
                horizontal: 8.0, vertical: 8.0),
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(
                    Radius.circular(4)),
                border: Border.all(
                    color: Theme.of(context)
                        .colorScheme
                        .surface,
                    width: 1)),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '${item['name']}'.tr,
                style: TextStyle(
                    fontSize: isDesktop ? 18 : 17,
                    fontFamily: 'kufi',
                    color: Theme.of(context)
                        .colorScheme
                        .secondary),
              ),
            ),
          ),
      ],
    );
  }
}
