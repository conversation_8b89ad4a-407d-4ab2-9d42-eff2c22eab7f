import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:quranic_insights/core/utils/io_utils.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

extension ShareAppExtension on void {
  Future<void> shareApp() async {
    if (kIsWeb) {
      // On web, just share text without image
      await Share.share(
        'تطبيق "القرآن الكريم - مكتبة الحكمة" التطبيق الأمثل لقراءة القرآن.\n\nللتحميل:\nalheekmahlib.com/#/download/app/0',
      );
    } else {
      final box = Get.context!.findRenderObject() as RenderBox?;
      final ByteData bytes =
          await rootBundle.load('assets/images/quran_banner.png');
      final Uint8List list = bytes.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/quran_banner.png';
      await IOUtils.instance.writeAsBytes(filePath, list);
      await Share.shareXFiles(
        [XFile(filePath)],
        text:
            'تطبيق "القرآن الكريم - مكتبة الحكمة" التطبيق الأمثل لقراءة القرآن.\n\nللتحميل:\nalheekmahlib.com/#/download/app/0',
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      );
    }
  }
}
