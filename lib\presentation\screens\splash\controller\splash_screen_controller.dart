part of '../splash.dart';

class SplashScreenController extends GetxController {
  static SplashScreenController get instance =>
      GetInstance().putOrFind(() => SplashScreenController());

  SplashState state = SplashState();

  @override
  void onInit() {
    _loadInitialData();
    startTime();
    
    // Add a safety timeout to force navigation if something gets stuck
    Future.delayed(const Duration(seconds: 6)).then((_) {
      print('[SplashScreen] Safety timeout reached, forcing navigation');
      if (Get.currentRoute != '/MainNavigationScreen') {
        Get.offAll(() => const MainNavigationScreen(),
            transition: Transition.fadeIn, curve: Curves.easeIn);
      }
    });
    
    Future.delayed(const Duration(milliseconds: 600))
        .then((_) => state.containerAnimate.value = true);
    Future.delayed(const Duration(seconds: 3)).then((_) {
      state.containerHeight.value = Get.height;
      state.containerHHeight.value = Get.height;
      state.customWidget.value = 0;
    });
    Future.delayed(const Duration(milliseconds: 2800))
        .then((_) => state.smallContainerHeight.value = Get.height);
    Future.delayed(const Duration(milliseconds: 2900))
        .then((_) => state.secondSmallContainerHeight.value = Get.height);
    Future.delayed(const Duration(milliseconds: 2950))
        .then((_) => state.thirdSmallContainerHeight.value = Get.height);
    super.onInit();
  }

  /// -------- [Methods] ----------

  Future<void> _loadInitialData() async {
    try {
      print('[SplashScreen] Loading initial data...');
      
      print('[SplashScreen] Loading TafsirAndTranslateController...');
      TafsirAndTranslateController.instance.loadTranslateValue();
      
      print('[SplashScreen] Loading SettingsController...');
      SettingsController.instance.loadLang();
      
      print('[SplashScreen] Loading GeneralController...');
      GeneralController.instance.getLastPageAndFontSize();
      
      print('[SplashScreen] Loading QuranController...');
      QuranController.instance.loadSwitchValue();
      QuranController.instance.getLastPage();
      
      print('[SplashScreen] Updating greeting...');
      GeneralController.instance.updateGreeting();
      
      print('[SplashScreen] Loading AudioController...');
      AudioController.instance.loadQuranReader();
      
      print('[SplashScreen] Setting screen selected value...');
      GeneralController.instance.state.screenSelectedValue.value =
          state.box.read(SCREEN_SELECTED_VALUE) ?? 0;
      
      print('[SplashScreen] Initial data loaded successfully');
    } catch (e, stackTrace) {
      print('[SplashScreen] Error loading initial data: $e');
      print('[SplashScreen] Stack trace: $stackTrace');
    }
  }

  Future startTime() async {
    try {
      print('[SplashScreen] Starting timer...');
      await Future.delayed(const Duration(seconds: 1));
      state.animate.value = true;
      print('[SplashScreen] Animation started, waiting 3 seconds...');
      await Future.delayed(const Duration(seconds: 3));
      print('[SplashScreen] Calling WhatsNewController.navigationPage()');
      WhatsNewController.instance.navigationPage();
      print('[SplashScreen] Navigation called');
    } catch (e, stackTrace) {
      print('[SplashScreen] Error in startTime: $e');
      print('[SplashScreen] Stack trace: $stackTrace');
      // Try to navigate directly
      Get.offAll(() => const MainNavigationScreen(),
          transition: Transition.fadeIn, curve: Curves.easeIn);
    }
  }

  Widget ramadhanOrEidGreeting() {
    if (state.today.hMonth == 9) {
      return ramadanOrEid('ramadan_white', height: 100.0);
    } else if (state.generalCtrl.eidDays) {
      return ramadanOrEid('eid_white', height: 100.0);
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget get customWidget {
    switch (state.customWidget.value) {
      case 0:
        return const LogoAndTitle();
      case 1:
        return WhatsNewScreen(
          newFeatures: WhatsNewController.instance.state.newFeatures,
        );
      default:
        return const LogoAndTitle();
    }
  }
}
